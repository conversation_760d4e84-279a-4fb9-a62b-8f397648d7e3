package com.dep.biguo.bean;

/**
 * 商品配套推荐数据模型
 */
public class ProductPackageBean {
    private String packageName;
    private String packageDesc;
    private float packagePrice;
    private boolean isSelected;

    public ProductPackageBean() {
    }

    public ProductPackageBean(String packageName, String packageDesc, float packagePrice) {
        this.packageName = packageName;
        this.packageDesc = packageDesc;
        this.packagePrice = packagePrice;
        this.isSelected = false;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public String getPackageDesc() {
        return packageDesc;
    }

    public void setPackageDesc(String packageDesc) {
        this.packageDesc = packageDesc;
    }

    public float getPackagePrice() {
        return packagePrice;
    }

    public void setPackagePrice(float packagePrice) {
        this.packagePrice = packagePrice;
    }

    public boolean isSelected() {
        return isSelected;
    }

    public void setSelected(boolean selected) {
        isSelected = selected;
    }
}
