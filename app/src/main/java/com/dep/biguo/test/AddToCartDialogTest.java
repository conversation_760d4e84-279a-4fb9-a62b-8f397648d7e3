package com.dep.biguo.test;

import com.dep.biguo.bean.ProductColorBean;
import com.dep.biguo.bean.ProductPackageBean;

/**
 * Simple test for AddToCartDialog functionality
 */
public class AddToCartDialogTest {
    
    public static void testProductColorBean() {
        ProductColorBean colorBean = new ProductColorBean("Gray", "#808080", "");
        colorBean.setSelected(true);
        
        System.out.println("Color: " + colorBean.getColorName());
        System.out.println("Selected: " + colorBean.isSelected());
    }
    
    public static void testProductPackageBean() {
        ProductPackageBean packageBean = new ProductPackageBean("128GB+16GB+Standard", "Basic Config", 0);
        packageBean.setSelected(true);
        
        System.out.println("Package: " + packageBean.getPackageName());
        System.out.println("Price: " + packageBean.getPackagePrice());
        System.out.println("Selected: " + packageBean.isSelected());
    }
    
    public static void main(String[] args) {
        System.out.println("Testing AddToCartDialog components...");
        testProductColorBean();
        testProductPackageBean();
        System.out.println("Tests completed successfully!");
    }
}
