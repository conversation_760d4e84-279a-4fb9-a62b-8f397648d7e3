package com.dep.biguo.mvp.ui.activity;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.viewpager.widget.ViewPager;

import com.dep.biguo.R;
import com.dep.biguo.bean.ProductDetailBean;
import com.dep.biguo.databinding.ActivityProductDetailBinding;
import com.dep.biguo.di.component.DaggerProductDetailComponent;
import com.dep.biguo.mvp.contract.ProductDetailContract;
import com.dep.biguo.mvp.presenter.ProductDetailPresenter;
import com.dep.biguo.mvp.ui.adapter.ProductImageAdapter;
import com.dep.biguo.mvp.ui.adapter.ReviewAdapter;
import com.dep.biguo.mvp.ui.adapter.TagAdapter;
import com.dep.biguo.mvp.ui.fragment.AddToCartDialogFragment;
import com.jess.arms.base.BaseLoadSirActivity;
import com.jess.arms.di.component.AppComponent;
import com.jess.arms.utils.ArmsUtils;

import java.util.ArrayList;
import java.util.List;

public class ProductDetailActivity extends BaseLoadSirActivity<ProductDetailPresenter>
        implements ProductDetailContract.View, View.OnClickListener {

    public static final String PRODUCT_ID = "product_id";

    private ActivityProductDetailBinding binding;
    private ProductImageAdapter imageAdapter;
    private ReviewAdapter reviewAdapter;
    private TagAdapter tagAdapter;
    private int productId;
    private ProductDetailBean currentProduct;

    public static void start(Context context, int productId) {
        Intent intent = new Intent(context, ProductDetailActivity.class);
        intent.putExtra(PRODUCT_ID, productId);
        context.startActivity(intent);
    }

    @Override
    public void setupActivityComponent(@NonNull AppComponent appComponent) {
        DaggerProductDetailComponent
                .builder()
                .appComponent(appComponent)
                .view(this)
                .build()
                .inject(this);
    }

    @Override
    public int initView(@Nullable Bundle savedInstanceState) {
        binding = DataBindingUtil.setContentView(this, R.layout.activity_product_detail);
        binding.setOnClickListener(this);
        return 0;
    }

    @Override
    public View initLoadSir() {
        return binding.getRoot();
    }

    @Override
    public void initData(@Nullable Bundle savedInstanceState) {
        productId = getIntent().getIntExtra(PRODUCT_ID, 1);

        setupToolbar();
        setupAdapters();
        setupViewPager();

        // 加载商品详情
        if (mPresenter != null) {
            mPresenter.getProductDetail(productId);
        }
    }

    private void setupToolbar() {
        setSupportActionBar(binding.toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setTitle("商品详情");
        }
    }

    private void setupAdapters() {
        // 图片适配器
        imageAdapter = new ProductImageAdapter(this, new ArrayList<>());

        // 评论适配器
        reviewAdapter = new ReviewAdapter(this, new ArrayList<>());
        binding.rvReviews.setLayoutManager(new LinearLayoutManager(this));
        binding.rvReviews.setAdapter(reviewAdapter);
        binding.rvReviews.setNestedScrollingEnabled(false);

        // 标签适配器
        tagAdapter = new TagAdapter(this, new ArrayList<>());
        binding.rvTags.setLayoutManager(new LinearLayoutManager(this, LinearLayoutManager.HORIZONTAL, false));
        binding.rvTags.setAdapter(tagAdapter);
    }

    private void setupViewPager() {
        binding.vpProductImages.setAdapter(imageAdapter);
        binding.vpProductImages.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {}

            @Override
            public void onPageSelected(int position) {
                updateImageIndicator(position);
                updateImageCount(position);
            }

            @Override
            public void onPageScrollStateChanged(int state) {}
        });
    }

    // Contract.View 接口实现
    @Override
    public Activity getActivity() {
        return this;
    }

    @Override
    public void setProductDetail(ProductDetailBean data) {
        currentProduct = data;

        // 更新商品信息
        binding.tvProductPrice.setText(String.format("¥%s", data.getPrice()));
        binding.tvOriginalPrice.setText(String.format("¥%s", data.getOriginalPrice()));
        binding.tvProductName.setText(data.getName());
        binding.tvProductDescription.setText(data.getDescription());
        binding.ratingBar.setRating(data.getRating());
        binding.tvRating.setText(String.valueOf(data.getRating()));
        binding.tvReviewCount.setText(String.format("(%d)", data.getReviewCount()));
        binding.tvReviewCountHeader.setText(String.format("(%d)", data.getReviewCount()));

        // 更新图片
        if (data.getImages() != null && !data.getImages().isEmpty()) {
            imageAdapter.updateImages(data.getImages());
            updateImageCount(0);
            setupImageIndicator(data.getImages().size());
        }

        // 更新标签
        if (data.getTags() != null && !data.getTags().isEmpty()) {
            tagAdapter.updateTags(data.getTags());
        }

        // 更新评论
        if (data.getReviews() != null && !data.getReviews().isEmpty()) {
            reviewAdapter.updateReviews(data.getReviews());
        }

        // 加载详情图片
        if (data.getDetailImage() != null) {
            // TODO: 使用Glide加载详情图片
            // Glide.with(this).load(data.getDetailImage()).into(binding.ivProductDetails);
        }
    }

    @Override
    public void setFavoriteStatus(boolean isFavorite) {
        // TODO: 更新收藏按钮状态
        // binding.ivFavorite.setImageResource(isFavorite ? R.drawable.ic_favorite : R.drawable.ic_favorite_border);
    }

    @Override
    public void showSpecDialog(ProductDetailBean product) {
        AddToCartDialogFragment dialog = AddToCartDialogFragment.newInstance(product);
        dialog.setOnAddToCartListener((quantity, specs) -> {
            if (mPresenter != null) {
                mPresenter.addToCart(quantity, specs);
            }
        });
        dialog.show(getSupportFragmentManager(), AddToCartDialogFragment.TAG);
    }

    @Override
    public void addToCartSuccess() {
        Toast.makeText(this, "添加到购物车成功", Toast.LENGTH_SHORT).show();
    }

    @Override
    public void showImagePreview(int position) {
        // TODO: 显示图片预览
        Toast.makeText(this, "图片预览: " + position, Toast.LENGTH_SHORT).show();
    }

    // 点击事件处理
    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.iv_favorite) {
            if (mPresenter != null) {
                mPresenter.toggleFavorite();
            }
        } else if (id == R.id.ll_customer_service) {
            // 客服咨询
            Toast.makeText(this, "联系客服", Toast.LENGTH_SHORT).show();
        } else if (id == R.id.ll_shopping_cart) {
            // 查看购物车
            Toast.makeText(this, "查看购物车", Toast.LENGTH_SHORT).show();
        } else if (id == R.id.btn_add_to_cart) {
            // 加入购物车
            if (mPresenter != null) {
                mPresenter.showSpecDialog();
            }
        } else if (id == R.id.btn_buy_now) {
            // 立即购买
            if (mPresenter != null) {
                mPresenter.showSpecDialog();
            }
        }
    }

    // 辅助方法
    private void updateImageIndicator(int position) {
        // TODO: 更新图片指示器
    }

    private void updateImageCount(int position) {
        if (currentProduct != null && currentProduct.getImages() != null) {
            int total = currentProduct.getImages().size();
            binding.tvImageCount.setText(String.format("%d/%d", position + 1, total));
        }
    }

    private void setupImageIndicator(int count) {
        // TODO: 设置图片指示器
        binding.llImageIndicator.removeAllViews();
        // 添加指示器点
    }

    @Override
    public boolean onSupportNavigateUp() {
        onBackPressed();
        return true;
    }
}