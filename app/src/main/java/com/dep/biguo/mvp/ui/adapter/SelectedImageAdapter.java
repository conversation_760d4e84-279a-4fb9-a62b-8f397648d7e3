package com.dep.biguo.mvp.ui.adapter;

import android.view.View;
import android.widget.ImageView;
import androidx.annotation.Nullable;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.dep.biguo.R;
import com.dep.biguo.utils.image.ImageLoader;
import java.util.ArrayList;
import java.util.List;

public class SelectedImageAdapter extends BaseQuickAdapter<String, BaseViewHolder> {

    private static final int VIEW_TYPE_IMAGE = 1;
    private static final int VIEW_TYPE_ADD = 2;

    public SelectedImageAdapter(@Nullable List<String> data) {
        super(data);
        addItemType(VIEW_TYPE_IMAGE, R.layout.item_selected_image);
        addItemType(VIEW_TYPE_ADD, R.layout.item_add_image);
    }

    @Override
    protected void convert(BaseViewHolder helper, String item) {
        switch (helper.getItemViewType()) {
            case VIEW_TYPE_IMAGE:
                ImageView ivImage = helper.getView(R.id.ivImage);
                ImageView ivDelete = helper.getView(R.id.ivDelete);

                // 加载图片
                ImageLoader.loadImage(ivImage, item);

                // 显示删除按钮
                ivDelete.setVisibility(View.VISIBLE);
                helper.addOnClickListener(R.id.ivDelete);
                break;
            case VIEW_TYPE_ADD:
                // 添加按钮不需要额外的数据绑定
                // 布局文件中已经设置了图标和文字
                break;
        }
    }

    @Override
    public int getDefItemViewType(int position) {
        if (mData.get(position).equals("add_button_placeholder")) {
            return VIEW_TYPE_ADD;
        }
        return VIEW_TYPE_IMAGE;
    }

    /**
     * 获取选中的图片路径列表（不包括添加按钮）
     */
    public List<String> getSelectedImages() {
        List<String> images = new ArrayList<>();
        for (String item : mData) {
            if (!item.equals("add_button_placeholder")) {
                images.add(item);
            }
        }
        return images;
    }

    /**
     * 获取选中图片的数量
     */
    public int getSelectedImageCount() {
        return getSelectedImages().size();
    }
}