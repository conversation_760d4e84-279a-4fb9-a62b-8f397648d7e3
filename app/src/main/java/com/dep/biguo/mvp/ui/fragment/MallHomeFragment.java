package com.dep.biguo.mvp.ui.fragment;

import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.os.Build;
import android.view.Window;
import android.view.WindowManager;
import android.graphics.Color;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.GridLayoutManager;

import com.dep.biguo.R;
import com.dep.biguo.bean.ShopBean;
import com.dep.biguo.databinding.MallHomeFragmentBinding;
import com.dep.biguo.mvp.ui.adapter.HotProductAdapter;
import com.dep.biguo.mvp.ui.adapter.MallProductAdapter;
import com.dep.biguo.mvp.ui.activity.ShopDetailActivity;
import com.dep.biguo.mvp.ui.activity.TextBooksActivity;
import com.dep.biguo.mvp.ui.activity.SecondHandActivity;
import com.dep.biguo.mvp.ui.activity.StudyToolActivity;
import com.dep.biguo.mvp.ui.activity.FarmAssistActivity;
import com.dep.biguo.mvp.ui.activity.ShopCartActivity;
import com.dep.biguo.mvp.ui.activity.AddressListActivity;
import com.dep.biguo.utils.MainAppUtils;
import com.google.android.material.tabs.TabLayout;
import com.jess.arms.base.BaseFragment;
import com.jess.arms.di.component.AppComponent;
import com.jess.arms.utils.ArmsUtils;
import com.youth.banner.adapter.BannerImageAdapter;
import com.youth.banner.holder.BannerImageHolder;
import com.youth.banner.indicator.CircleIndicator;

import java.util.ArrayList;
import java.util.List;

public class MallHomeFragment extends BaseFragment implements View.OnClickListener {

    private static final String TAG = "MallHomeFragment";

    private MallHomeFragmentBinding binding;
    private HotProductAdapter hotProductAdapter;
    private MallProductAdapter allProductAdapter;

    public static MallHomeFragment newInstance() {
        Log.d(TAG, "newInstance() called");
        return new MallHomeFragment();
    }

    @Override
    public void setupFragmentComponent(@NonNull AppComponent appComponent) {
        Log.d(TAG, "setupFragmentComponent() called");
        // 不需要注入，这是一个简单的UI页面
    }

    @Override
    public View initView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        Log.d(TAG, "initView() called");
        binding = DataBindingUtil.inflate(inflater, R.layout.mall_home_fragment, container, false);
        if (binding == null) {
            Log.e(TAG, "binding is null! Layout inflation failed!");
            return null;
        }
        Log.d(TAG, "binding created successfully");
        binding.setOnClickListener(this);

        setTranslucentStatusBar();

        // 设置顶部渐变色区域paddingTop为状态栏高度
        LinearLayout topLayout = (LinearLayout) binding.getRoot().findViewById(R.id.mallHomeTopLayout);
        if (topLayout != null) {
            int statusBarHeight = getStatusBarHeight();
            topLayout.setPadding(
                topLayout.getPaddingLeft(),
                statusBarHeight,
                topLayout.getPaddingRight(),
                topLayout.getPaddingBottom()
            );
        }

        return binding.getRoot();
    }

    private void setTranslucentStatusBar() {
        if (getActivity() == null) return;
        Window window = getActivity().getWindow();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
            window.getDecorView().setSystemUiVisibility(
                View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN | View.SYSTEM_UI_FLAG_LAYOUT_STABLE
            );
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
            window.setStatusBarColor(Color.TRANSPARENT);
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            window.addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
        }
    }

    private int getStatusBarHeight() {
        int result = 0;
        int resourceId = getResources().getIdentifier("status_bar_height", "dimen", "android");
        if (resourceId > 0) {
            result = getResources().getDimensionPixelSize(resourceId);
        }
        return result;
    }

    @Override
    public void initData(@Nullable Bundle savedInstanceState) {
        Log.d(TAG, "initData() called");
        setupBanner();
        setupTabs();
        loadMockData();
    }

    private void setupBanner() {
        List<Integer> bannerImages = new ArrayList<>();
        bannerImages.add(R.drawable.bg_gradient_red);
        bannerImages.add(R.drawable.bg_gradient_red);
        bannerImages.add(R.drawable.bg_gradient_red);

        binding.banner.setAdapter(new BannerImageAdapter<Integer>(bannerImages) {
            @Override
            public void onBindView(BannerImageHolder holder, Integer data, int position, int size) {
                holder.imageView.setImageResource(data);
            }
        }).setIndicator(new CircleIndicator(getContext()));
    }

    private void setupTabs() {
        binding.tabLayout.addTab(binding.tabLayout.newTab().setText("学习工具"));
        binding.tabLayout.addTab(binding.tabLayout.newTab().setText("二手教材"));
        binding.tabLayout.addTab(binding.tabLayout.newTab().setText("助农项目"));
        binding.tabLayout.addTab(binding.tabLayout.newTab().setText("周边产品"));

        // 添加Tab选择监听器
        binding.tabLayout.addOnTabSelectedListener(new TabLayout.OnTabSelectedListener() {
            @Override
            public void onTabSelected(TabLayout.Tab tab) {
                int position = tab.getPosition();
                switch (position) {
                    case 0: // 学习工具
                        ArmsUtils.startActivity(StudyToolActivity.class);
                        break;
                    case 1: // 二手教材
                        ArmsUtils.startActivity(SecondHandActivity.class);
                        break;
                    case 2: // 助农项目
                        ArmsUtils.startActivity(FarmAssistActivity.class);
                        break;
                    case 3: // 周边产品
                        // TODO: 实现周边产品页面导航
                        ArmsUtils.snackbarText("周边产品功能开发中...");
                        break;
                }
            }

            @Override
            public void onTabUnselected(TabLayout.Tab tab) {
                // 可以在这里处理tab取消选择的逻辑
            }

            @Override
            public void onTabReselected(TabLayout.Tab tab) {
                // 可以在这里处理tab重复选择的逻辑
            }
        });
    }

    private void loadMockData() {
        Log.d(TAG, "loadMockData() called");

        try {
            // 模拟热卖商品数据
            List<ShopBean> hotProducts = new ArrayList<>();
            for (int i = 0; i < 3; i++) {
                ShopBean bean = new ShopBean();
                bean.setId(i + 1);
                bean.setName("笔果AI学习机");
                bean.setPrice(i == 0 ? "2500.00" : "118.00");
                hotProducts.add(bean);
            }
            hotProductAdapter = new HotProductAdapter(hotProducts);
            binding.rvHotProducts.setLayoutManager(new LinearLayoutManager(getContext(), LinearLayoutManager.HORIZONTAL, false));
            binding.rvHotProducts.setAdapter(hotProductAdapter);

            // 模拟全部商品数据
            List<ShopBean> allProducts = new ArrayList<>();
            for (int i = 0; i < 10; i++) {
                ShopBean bean = new ShopBean();
                bean.setId(i + 10);
                bean.setName(i % 2 == 0 ? "这里是商品名称最多展示两行文字" : "自考点读笔");
                bean.setPrice("128.00");
                bean.setSales(8000);
                allProducts.add(bean);
            }
            allProductAdapter = new MallProductAdapter(allProducts);
            binding.rvAllProducts.setLayoutManager(new GridLayoutManager(getContext(), 2));
            binding.rvAllProducts.setAdapter(allProductAdapter);

            // 设置点击事件
            hotProductAdapter.setOnItemClickListener((adapter, view, position) -> {
                ShopBean item = hotProductAdapter.getItem(position);
                if (item != null) {
                    ShopDetailActivity.Start(getContext(), item.getId());
                }
            });

            allProductAdapter.setOnItemClickListener((adapter, view, position) -> {
                ShopBean item = allProductAdapter.getItem(position);
                if (item != null) {
                    ShopDetailActivity.Start(getContext(), item.getId());
                }
            });

        } catch (Exception e) {
            Log.e(TAG, "Error in loadMockData(): " + e.getMessage(), e);
        }
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();

        if (id == R.id.ivShoppingCart) {
            ArmsUtils.startActivity(ShopCartActivity.class);
        }
        // TODO: Add other click listeners if needed
    }

    @Override
    public void setData(@Nullable Object data) {
        // 可以接收外部传入的数据
    }
}
