package com.dep.biguo.mvp.ui.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.dep.biguo.R;
import com.dep.biguo.bean.ProductColorBean;
import com.dep.biguo.utils.ImageLoader;
import java.util.List;

/**
 * 商品颜色选择适配器
 */
public class ProductColorAdapter extends RecyclerView.Adapter<ProductColorAdapter.ColorViewHolder> {

    private List<ProductColorBean> colorList;
    private OnColorSelectedListener onColorSelectedListener;
    private int selectedPosition = 0; // 默认选中第一个

    public ProductColorAdapter(List<ProductColorBean> colorList) {
        this.colorList = colorList;
        if (colorList != null && !colorList.isEmpty()) {
            colorList.get(0).setSelected(true);
        }
    }

    @NonNull
    @Override
    public ColorViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_product_color, parent, false);
        return new ColorViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ColorViewHolder holder, int position) {
        ProductColorBean colorBean = colorList.get(position);
        
        // 设置颜色名称
        holder.tvColorName.setText(colorBean.getColorName());
        
        // 加载颜色图片
        if (colorBean.getColorImage() != null && !colorBean.getColorImage().isEmpty()) {
            ImageLoader.loadImage(holder.ivColorImage, colorBean.getColorImage());
        } else {
            // 如果没有图片，使用颜色代码设置背景色
            holder.ivColorImage.setImageResource(R.drawable.bg_round_8_gray);
        }
        
        // 设置选中状态
        if (colorBean.isSelected()) {
            holder.itemView.setBackgroundResource(R.drawable.bg_product_color_selected);
            holder.tvColorName.setTextColor(holder.itemView.getContext().getResources().getColor(R.color.theme));
        } else {
            holder.itemView.setBackgroundResource(R.drawable.bg_product_color_normal);
            holder.tvColorName.setTextColor(holder.itemView.getContext().getResources().getColor(R.color.tblack));
        }
        
        // 设置点击事件
        holder.itemView.setOnClickListener(v -> {
            // 取消之前选中的项
            if (selectedPosition < colorList.size()) {
                colorList.get(selectedPosition).setSelected(false);
            }
            
            // 设置新选中的项
            selectedPosition = position;
            colorBean.setSelected(true);
            
            // 刷新适配器
            notifyDataSetChanged();
            
            // 回调选中事件
            if (onColorSelectedListener != null) {
                onColorSelectedListener.onColorSelected(colorBean, position);
            }
        });
    }

    @Override
    public int getItemCount() {
        return colorList != null ? colorList.size() : 0;
    }

    public ProductColorBean getSelectedColor() {
        if (selectedPosition < colorList.size()) {
            return colorList.get(selectedPosition);
        }
        return null;
    }

    public void setOnColorSelectedListener(OnColorSelectedListener listener) {
        this.onColorSelectedListener = listener;
    }

    static class ColorViewHolder extends RecyclerView.ViewHolder {
        ImageView ivColorImage;
        TextView tvColorName;

        public ColorViewHolder(@NonNull View itemView) {
            super(itemView);
            ivColorImage = itemView.findViewById(R.id.iv_color_image);
            tvColorName = itemView.findViewById(R.id.tv_color_name);
        }
    }

    public interface OnColorSelectedListener {
        void onColorSelected(ProductColorBean colorBean, int position);
    }
}
