package com.dep.biguo.mvp.ui.fragment;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.dep.biguo.R;
import com.dep.biguo.bean.ProductColorBean;
import com.dep.biguo.bean.ProductDetailBean;
import com.dep.biguo.bean.ProductPackageBean;
import com.dep.biguo.mvp.ui.adapter.ProductColorAdapter;
import com.dep.biguo.utils.ImageLoader;
import com.google.android.material.bottomsheet.BottomSheetDialogFragment;
import com.google.android.material.chip.Chip;
import com.google.android.material.chip.ChipGroup;
import java.util.ArrayList;
import java.util.List;

public class AddToCartDialogFragment extends BottomSheetDialogFragment implements View.OnClickListener {

    public static final String TAG = "AddToCartDialogFragment";

    // UI组件
    private TextView tvAddress;
    private ImageView ivClose;
    private ImageView ivProductImage;
    private TextView tvProductPrice;
    private TextView tvQuantity;
    private TextView tvReduce;
    private TextView tvAdd;
    private RecyclerView rvColors;
    private ChipGroup cgPackages;
    private Button btnAddToCartConfirm;

    // 数据
    private ProductDetailBean productDetail;
    private List<ProductColorBean> colorList;
    private List<ProductPackageBean> packageList;
    private ProductColorAdapter colorAdapter;
    private int quantity = 1;
    private int maxQuantity = 5; // 限购数量

    // 回调接口
    private OnAddToCartListener onAddToCartListener;

    public static AddToCartDialogFragment newInstance(ProductDetailBean productDetail) {
        AddToCartDialogFragment fragment = new AddToCartDialogFragment();
        Bundle args = new Bundle();
        args.putSerializable("productDetail", productDetail);
        fragment.setArguments(args);
        return fragment;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.dialog_add_to_cart, container, false);
        initViews(view);
        initData();
        setupViews();
        return view;
    }

    private void initViews(View view) {
        tvAddress = view.findViewById(R.id.tv_address);
        ivClose = view.findViewById(R.id.iv_close);
        ivProductImage = view.findViewById(R.id.iv_product_image);
        tvProductPrice = view.findViewById(R.id.tv_product_price);
        tvQuantity = view.findViewById(R.id.tv_quantity);
        tvReduce = view.findViewById(R.id.tv_reduce);
        tvAdd = view.findViewById(R.id.tv_add);
        rvColors = view.findViewById(R.id.rv_colors);
        cgPackages = view.findViewById(R.id.cg_packages);
        btnAddToCartConfirm = view.findViewById(R.id.btn_add_to_cart_confirm);

        // 设置点击事件
        ivClose.setOnClickListener(this);
        tvReduce.setOnClickListener(this);
        tvAdd.setOnClickListener(this);
        btnAddToCartConfirm.setOnClickListener(this);
    }

    private void initData() {
        if (getArguments() != null) {
            productDetail = (ProductDetailBean) getArguments().getSerializable("productDetail");
        }

        // Initialize color data (mock data, should be obtained from productDetail)
        colorList = new ArrayList<>();
        colorList.add(new ProductColorBean("Gray", "#808080", ""));
        colorList.add(new ProductColorBean("Silver", "#C0C0C0", ""));
        colorList.add(new ProductColorBean("Blue", "#0000FF", ""));

        // Initialize package recommendation data (mock data, should be obtained from productDetail)
        packageList = new ArrayList<>();
        packageList.add(new ProductPackageBean("128GB+16GB+Standard", "Basic Config", 0));
        packageList.add(new ProductPackageBean("256GB+16GB+Official", "Recommended Config", 500));
        packageList.add(new ProductPackageBean("512GB+32GB+Flagship", "High-end Config", 1000));
    }

    private void setupViews() {
        // Set product information
        if (productDetail != null) {
            if (productDetail.getImages() != null && !productDetail.getImages().isEmpty()) {
                ImageLoader.loadImage(ivProductImage, productDetail.getImages().get(0));
            }
            tvProductPrice.setText("¥" + productDetail.getPrice());
        } else {
            // Use default data
            tvProductPrice.setText("¥2500.00");
        }

        // Set quantity
        updateQuantityDisplay();

        // Set color selection
        setupColorSelection();

        // Set package recommendation
        setupPackageSelection();
    }

    private void setupColorSelection() {
        colorAdapter = new ProductColorAdapter(colorList);
        rvColors.setLayoutManager(new LinearLayoutManager(getContext(), LinearLayoutManager.HORIZONTAL, false));
        rvColors.setAdapter(colorAdapter);

        colorAdapter.setOnColorSelectedListener((colorBean, position) -> {
            // Color selection callback handling
        });
    }

    private void setupPackageSelection() {
        cgPackages.removeAllViews();

        for (int i = 0; i < packageList.size(); i++) {
            ProductPackageBean packageBean = packageList.get(i);
            Chip chip = new Chip(getContext());
            chip.setText(packageBean.getPackageName());
            chip.setCheckable(true);
            chip.setChecked(i == 0); // Default select first one

            final int position = i;
            chip.setOnCheckedChangeListener((buttonView, isChecked) -> {
                if (isChecked) {
                    // Cancel other selected states
                    for (int j = 0; j < packageList.size(); j++) {
                        packageList.get(j).setSelected(j == position);
                    }
                }
            });

            cgPackages.addView(chip);
        }

        // Default select first one
        if (!packageList.isEmpty()) {
            packageList.get(0).setSelected(true);
        }
    }

    private void updateQuantityDisplay() {
        tvQuantity.setText(String.valueOf(quantity));

        // Update reduce button state
        tvReduce.setEnabled(quantity > 1);
        tvReduce.setAlpha(quantity > 1 ? 1.0f : 0.5f);

        // Update add button state
        tvAdd.setEnabled(quantity < maxQuantity);
        tvAdd.setAlpha(quantity < maxQuantity ? 1.0f : 0.5f);
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.iv_close) {
            dismiss();
        } else if (id == R.id.tv_reduce) {
            if (quantity > 1) {
                quantity--;
                updateQuantityDisplay();
            }
        } else if (id == R.id.tv_add) {
            if (quantity < maxQuantity) {
                quantity++;
                updateQuantityDisplay();
            }
        } else if (id == R.id.btn_add_to_cart_confirm) {
            handleAddToCart();
        }
    }

    private void handleAddToCart() {
        // Get selected color
        ProductColorBean selectedColor = colorAdapter.getSelectedColor();

        // Get selected package recommendation
        ProductPackageBean selectedPackage = null;
        for (ProductPackageBean packageBean : packageList) {
            if (packageBean.isSelected()) {
                selectedPackage = packageBean;
                break;
            }
        }

        // Build specs string
        StringBuilder specs = new StringBuilder();
        if (selectedColor != null) {
            specs.append("Color:").append(selectedColor.getColorName());
        }
        if (selectedPackage != null) {
            if (specs.length() > 0) specs.append(";");
            specs.append("Config:").append(selectedPackage.getPackageName());
        }

        // Callback add to cart event
        if (onAddToCartListener != null) {
            onAddToCartListener.onAddToCart(quantity, specs.toString());
        }

        dismiss();
    }

    public void setOnAddToCartListener(OnAddToCartListener listener) {
        this.onAddToCartListener = listener;
    }

    public interface OnAddToCartListener {
        void onAddToCart(int quantity, String specs);
    }
}