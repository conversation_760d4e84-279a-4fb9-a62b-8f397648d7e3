package com.dep.biguo.mvp.ui.activity;

import android.graphics.Color;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ArrayAdapter;
import android.widget.Toast;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.databinding.DataBindingUtil;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.dep.biguo.R;
import com.dep.biguo.databinding.PublishBookActivityBinding;
import com.dep.biguo.mvp.ui.adapter.SelectedImageAdapter;
import com.dep.biguo.utils.PictureSelectorPermission;
import com.dep.biguo.utils.image.SaveToCacheDirCustomTarget;
import com.luck.picture.lib.PictureSelector;
import com.luck.picture.lib.config.SelectMimeType;
import com.luck.picture.lib.config.SelectModeConfig;
import com.luck.picture.lib.entity.LocalMedia;
import com.luck.picture.lib.interfaces.OnResultCallbackListener;
import com.luck.picture.lib.utils.ToastUtils;

import java.util.ArrayList;
import java.util.List;

public class PublishBookActivity extends AppCompatActivity implements View.OnClickListener {

    private PublishBookActivityBinding binding;
    private SelectedImageAdapter imageAdapter;
    private List<String> selectedImages = new ArrayList<>();

    private static final int MAX_IMAGE_COUNT = 9; // 最多9张图片
    private static final int MAX_VIDEO_COUNT = 1; // 最多1个视频

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = DataBindingUtil.setContentView(this, R.layout.publish_book_activity);
        binding.setOnClickListener(this);

        setStatusBarColor();
        initImageRecyclerView();
        initConditionSpinner();
    }

    private void setStatusBarColor() {
        Window window = getWindow();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
            window.getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_STABLE);
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
            window.setStatusBarColor(Color.WHITE);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                window.getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR | View.SYSTEM_UI_FLAG_LAYOUT_STABLE);
            }
        }
    }

    private void initImageRecyclerView() {
        // Add a placeholder for the "add" button
        selectedImages.add("add_button_placeholder");
        imageAdapter = new SelectedImageAdapter(selectedImages);
        binding.rvImages.setLayoutManager(new LinearLayoutManager(this, RecyclerView.HORIZONTAL, false));
        binding.rvImages.setAdapter(imageAdapter);

        imageAdapter.setOnItemChildClickListener((adapter, view, position) -> {
            if (view.getId() == R.id.ivDelete) {
                selectedImages.remove(position);
                imageAdapter.notifyItemRemoved(position);
                // 确保添加按钮始终存在
                if (!selectedImages.contains("add_button_placeholder")) {
                    selectedImages.add("add_button_placeholder");
                    imageAdapter.notifyItemInserted(selectedImages.size() - 1);
                }
            }
        });

        imageAdapter.setOnItemClickListener((adapter, view, position) -> {
            if (selectedImages.get(position).equals("add_button_placeholder")) {
                selectMedia();
            }
        });
    }

    private void initConditionSpinner() {
        String[] conditions = {"全新", "九成新", "八成新", "七成新", "六成新", "五成新"};
        ArrayAdapter<String> adapter = new ArrayAdapter<>(this, android.R.layout.simple_spinner_item, conditions);
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        binding.spinnerCondition.setAdapter(adapter);
    }


    /**
     * 选择图片或视频
     */
    private void selectMedia() {
        // 计算当前已选择的图片数量（排除添加按钮）
        int currentImageCount = selectedImages.size() - 1;

        if (currentImageCount >= MAX_IMAGE_COUNT) {
            Toast.makeText(this, "最多只能选择" + MAX_IMAGE_COUNT + "张图片", Toast.LENGTH_SHORT).show();
            return;
        }

        // 选择图片
        PictureSelector.create(this)
                .openGallery(SelectMimeType.ofImage())
                .setSelectionMode(SelectModeConfig.MULTIPLE)
                .setMaxSelectNum(MAX_IMAGE_COUNT - currentImageCount)
                .setVideoThumbnailListener((context, contentPath, callbackListener) -> {
                    SaveToCacheDirCustomTarget customTarget = new SaveToCacheDirCustomTarget(context, contentPath, callbackListener);
                    Glide.with(context)
                            .asBitmap()
                            .load(contentPath)
                            .into(customTarget);
                })
                .setPermissionsInterceptListener(PictureSelectorPermission.create())
                .forResult(new OnResultCallbackListener<LocalMedia>() {
                    @Override
                    public void onResult(ArrayList<LocalMedia> result) {
                        handleSelectedMedia(result);
                    }

                    @Override
                    public void onCancel() {
                        // 用户取消选择
                    }
                });
    }

    /**
     * 处理选择的媒体文件
     */
    private void handleSelectedMedia(ArrayList<LocalMedia> mediaList) {
        for (LocalMedia media : mediaList) {
            String path = "";
            if (!TextUtils.isEmpty(media.getCompressPath())) {
                path = media.getCompressPath();
            } else if (!TextUtils.isEmpty(media.getRealPath())) {
                path = media.getRealPath();
            } else if (!TextUtils.isEmpty(media.getPath())) {
                path = media.getPath();
            }

            if (!TextUtils.isEmpty(path)) {
                // 在添加按钮之前插入新图片
                int insertPosition = selectedImages.size() - 1;
                selectedImages.add(insertPosition, path);
                imageAdapter.notifyItemInserted(insertPosition);
            }
        }

        // 如果达到最大数量，移除添加按钮
        if (selectedImages.size() - 1 >= MAX_IMAGE_COUNT) {
            selectedImages.remove("add_button_placeholder");
            imageAdapter.notifyItemRemoved(selectedImages.size());
        }
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();

        if (id == R.id.ivBack) {
            finish();
        } else if (id == R.id.btnPublish) {
            publishBook();
        }
    }

    private void publishBook() {
        // 验证图片
        if (imageAdapter.getSelectedImageCount() == 0) {
            Toast.makeText(this, "请至少添加一张图片", Toast.LENGTH_SHORT).show();
            return;
        }

        String bookName = binding.etBookName.getText().toString().trim();
        String isbn = binding.etIsbn.getText().toString().trim();
        String purchasePrice = binding.etPurchasePrice.getText().toString().trim();
        String sellingPrice = binding.etSellingPrice.getText().toString().trim();
        String description = binding.etDescription.getText().toString().trim();

        if (bookName.isEmpty()) {
            Toast.makeText(this, "请填写教材名称", Toast.LENGTH_SHORT).show();
            return;
        }

        if (isbn.isEmpty()) {
            Toast.makeText(this, "请填写ISBN号", Toast.LENGTH_SHORT).show();
            return;
        }

        if (purchasePrice.isEmpty()) {
            Toast.makeText(this, "请填写购入价格", Toast.LENGTH_SHORT).show();
            return;
        }

        if (sellingPrice.isEmpty()) {
            Toast.makeText(this, "请填写定价", Toast.LENGTH_SHORT).show();
            return;
        }

        if (description.isEmpty()) {
            Toast.makeText(this, "请填写教材描述", Toast.LENGTH_SHORT).show();
            return;
        }

        if (!binding.cbTerms.isChecked()) {
            Toast.makeText(this, "请同意相关条款", Toast.LENGTH_SHORT).show();
            return;
        }

        // TODO: 实现实际的发布逻辑
        // 这里应该调用API上传图片和教材信息
        publishBookToServer();
    }

    private void publishBookToServer() {
        // 获取所有表单数据
        List<String> imagePaths = imageAdapter.getSelectedImages();
        String bookName = binding.etBookName.getText().toString().trim();
        String isbn = binding.etIsbn.getText().toString().trim();
        String condition = binding.spinnerCondition.getSelectedItem().toString();
        String purchasePrice = binding.etPurchasePrice.getText().toString().trim();
        String sellingPrice = binding.etSellingPrice.getText().toString().trim();
        String description = binding.etDescription.getText().toString().trim();

        // TODO: 实现服务器发布逻辑
        // 1. 上传图片到服务器
        // for (String imagePath : imagePaths) {
        //     uploadImage(imagePath);
        // }
        // 2. 创建教材信息对象
        // SecondHandBook book = new SecondHandBook();
        // book.setName(bookName);
        // book.setIsbn(isbn);
        // book.setCondition(condition);
        // book.setPurchasePrice(purchasePrice);
        // book.setSellingPrice(sellingPrice);
        // book.setDescription(description);
        // book.setImageUrls(uploadedImageUrls);
        // 3. 提交到二手教材市场
        // publishSecondHandBook(book);

        Toast.makeText(this, "发布成功！已添加" + imagePaths.size() + "张图片", Toast.LENGTH_SHORT).show();
        finish();
    }
}