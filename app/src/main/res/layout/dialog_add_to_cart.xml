<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/bg_top_round_20_white"
    android:padding="16dp">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/tv_address"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"
            android:drawableStart="@drawable/icon_location_small"
            android:drawablePadding="8dp"
            android:text="TOD科技中心2栋"
            android:textColor="@android:color/black"
            android:textSize="16sp" />

        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:src="@drawable/ic_close_black_24dp" />
    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_product_image"
            android:layout_width="100dp"
            android:layout_height="100dp"
            android:scaleType="centerCrop" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_product_price"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="¥2500.00"
                android:textColor="@android:color/holo_red_dark"
                android:textSize="20sp"
                android:textStyle="bold" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:gravity="center_vertical">

                <TextView
                    android:id="@+id/tv_reduce"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="—"
                    android:textSize="20sp"
                    android:background="?android:attr/selectableItemBackground"
                    android:padding="8dp" />

                <TextView
                    android:id="@+id/tv_quantity"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:text="1"
                    android:textSize="18sp" />

                <TextView
                    android:id="@+id/tv_add"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="+"
                    android:textSize="20sp"
                    android:background="?android:attr/selectableItemBackground"
                    android:padding="8dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:text="(限购5件)有货" />
            </LinearLayout>
        </LinearLayout>
    </LinearLayout>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="商品颜色"
        android:textColor="@android:color/black"
        android:textSize="16sp" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_colors"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="配套推荐"
        android:textColor="@android:color/black"
        android:textSize="16sp" />

    <com.google.android.material.chip.ChipGroup
        android:id="@+id/cg_packages"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        app:singleSelection="true" />

    <Button
        android:id="@+id/btn_add_to_cart_confirm"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:background="@drawable/bg_add_to_cart_button"
        android:text="加入购物车"
        android:textColor="@android:color/white" />

</LinearLayout>